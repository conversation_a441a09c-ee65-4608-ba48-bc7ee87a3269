<?php

namespace App\Filament\Resources\CampaignResource\Actions;

use App\Jobs\SyncVideosJob;
use App\Models\Campaign;
use App\Services\TikTok\TikTokApiService;
use App\Services\TikTok\VideoSyncService;
use Exception;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TagsInput;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Notifications\Notification;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables\Actions\BulkAction;
use Illuminate\Database\Eloquent\Collection;

class BulkSyncVideosAction extends BulkAction
{
    public static function getDefaultName(): ?string
    {
        return 'bulk_sync_videos';
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->label('Sync Videos')
            ->icon('heroicon-o-video-camera')
            ->color('info')
            ->modalHeading('Bulk Sync Videos for Selected Campaigns')
            ->modalDescription('Sync videos from TikTok API for all selected campaigns. This process may take several minutes.')
            ->modalWidth(MaxWidth::FourExtraLarge)
            ->form($this->getFormSchema())
            ->action(function (Collection $records, array $data) {
                $this->handleBulkSyncVideos($records, $data);
            });
    }

    protected function getFormSchema(): array
    {
        return [
            Section::make('Sync Options')
                ->description('Configure options for bulk video synchronization')
                ->schema([
                    Grid::make()
                        ->schema([
                            Select::make('sync_method')
                                ->label('Sync Method')
                                ->options([
                                    'queue' => 'Background Queue (Recommended)',
                                    'direct' => 'Direct Sync (Immediate)',
                                ])
                                ->default('queue')
                                ->helperText('Queue method runs in background and prevents timeouts'),

                            TextInput::make('page_size')
                                ->label('Page Size')
                                ->numeric()
                                ->default(20)
                                ->minValue(1)
                                ->maxValue(50)
                                ->helperText('Number of videos per page (1-50)'),

                            TextInput::make('max_pages')
                                ->label('Max Pages')
                                ->numeric()
                                ->default(5)
                                ->minValue(1)
                                ->maxValue(20)
                                ->helperText('Maximum pages to fetch per campaign'),

                            Select::make('sort_field')
                                ->label('Sort Field')
                                ->options([
                                    'GMV' => 'GMV',
                                    'POST_TIME' => 'Post Time',
                                    'VIDEO_VIEWS' => 'Video Views',
                                    'VIDEO_LIKES' => 'Video Likes',
                                    'CLICK_THROUGH_RATE' => 'Click-through Rate',
                                    'PRODUCT_CLICKS' => 'Product Clicks',
                                ])
                                ->default('GMV'),

                            Select::make('sort_type')
                                ->label('Sort Order')
                                ->options([
                                    'DESC' => 'Descending',
                                    'ASC' => 'Ascending',
                                ])
                                ->default('DESC'),

                            Toggle::make('custom_posts_eligible')
                                ->label('Custom Posts Only')
                                ->helperText('Only sync videos eligible for custom posts'),
                        ]),

                    Grid::make(1)
                        ->schema([
                            TagsInput::make('spu_id_list')
                                ->label('Product SPU IDs (Optional)')
                                ->placeholder('Enter SPU IDs to filter by specific products')
                                ->helperText('Leave empty to sync all products. Max 50 items')
                                ->separator(','),
                        ]),

                    Grid::make(2)
                        ->schema([
                            Toggle::make('need_auth_code_video')
                                ->label('Include AUTH_CODE Videos')
                                ->helperText('Include posts associated with AUTH_CODE identities')
                                ->default(false),

                            Toggle::make('force_sync')
                                ->label('Force Sync')
                                ->helperText('Sync even if recently synced')
                                ->default(false),
                        ]),
                ]),

            Section::make('Notification Settings')
                ->description('Configure notifications for sync completion')
                ->collapsed()
                ->schema([
                    Toggle::make('notify_completion')
                        ->label('Notify on Completion')
                        ->default(true)
                        ->helperText('Send notification when sync completes'),
                ]),
        ];
    }

    protected function handleBulkSyncVideos(Collection $records, array $data): void
    {
        try {
            // Validate campaigns have required data
            $validCampaigns = $records->filter(function (Campaign $campaign) {
                // Check if campaign has shop_id and shop has store_authorized_bc_id
                if ($campaign->shop_id && $campaign->shop && $campaign->shop->store_authorized_bc_id) {
                    return true;
                }

                // Fallback: check if campaign has direct store_id and store_authorized_bc_id
                return $campaign->store_id && $campaign->store_authorized_bc_id;
            });

            if ($validCampaigns->isEmpty()) {
                Notification::make()
                    ->title('No Valid Campaigns')
                    ->body('None of the selected campaigns have the required store information for video sync.')
                    ->warning()
                    ->send();
                return;
            }

            $invalidCount = $records->count() - $validCampaigns->count();
            if ($invalidCount > 0) {
                Notification::make()
                    ->title('Some Campaigns Skipped')
                    ->body("{$invalidCount} campaigns were skipped due to missing store information.")
                    ->warning()
                    ->send();
            }

            // Build sync options
            $syncOptions = [
                'page_size' => min(max(1, (int)($data['page_size'] ?? 20)), 50),
                'max_pages' => min(max(1, (int)($data['max_pages'] ?? 5)), 20),
            ];

            if (!empty($data['spu_id_list'])) {
                $syncOptions['spu_id_list'] = array_filter($data['spu_id_list']);
            }

            if (isset($data['custom_posts_eligible'])) {
                $syncOptions['custom_posts_eligible'] = (bool)$data['custom_posts_eligible'];
            }

            if (isset($data['need_auth_code_video'])) {
                $syncOptions['need_auth_code_video'] = (bool)$data['need_auth_code_video'];
            }

            if (!empty($data['sort_field'])) {
                $syncOptions['sort_field'] = $data['sort_field'];
            }

            if (!empty($data['sort_type'])) {
                $syncOptions['sort_type'] = $data['sort_type'];
            }

            $campaignIds = $validCampaigns->pluck('campaign_id')->toArray();
            $notifyCompletion = $data['notify_completion'] ?? true;

            if ($data['sync_method'] === 'queue') {
                // Dispatch to queue
                SyncVideosJob::dispatch(
                    'multiple',
                    null,
                    $campaignIds,
                    $syncOptions,
                    $notifyCompletion
                );

                Notification::make()
                    ->title('Video Sync Queued')
                    ->body("Video sync for {$validCampaigns->count()} campaigns has been queued for background processing.")
                    ->success()
                    ->duration(8000)
                    ->send();

            } else {
                // Direct sync (not recommended for many campaigns)
                if ($validCampaigns->count() > 5) {
                    Notification::make()
                        ->title('Too Many Campaigns for Direct Sync')
                        ->body('Direct sync is not recommended for more than 5 campaigns. Please use queue method.')
                        ->warning()
                        ->send();
                    return;
                }

                $this->performDirectSync($campaignIds, $syncOptions);
            }

        } catch (Exception $e) {
            Notification::make()
                ->title('Bulk Video Sync Error')
                ->body('An unexpected error occurred: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    protected function performDirectSync(array $campaignIds, array $syncOptions): void
    {
        try {
            $apiService = new TikTokApiService();
            if (!$apiService->isConfigured()) {
                $error = $apiService->getLastError();
                Notification::make()
                    ->title('TikTok API Configuration Error')
                    ->body($error['message'] ?? 'TikTok API is not properly configured')
                    ->danger()
                    ->send();
                return;
            }

            $videoSyncService = new VideoSyncService($apiService);
            $result = $videoSyncService->syncVideosForMultipleCampaigns($campaignIds, $syncOptions);

            if (isset($result['overall_stats'])) {
                $stats = $result['overall_stats'];
                $message = sprintf(
                    'Successfully processed %d campaigns with %d total videos (%d created, %d updated)',
                    $stats['campaigns_success'] ?? 0,
                    $stats['total_videos'] ?? 0,
                    $stats['total_created'] ?? 0,
                    $stats['total_updated'] ?? 0
                );

                if ($stats['campaigns_failed'] > 0) {
                    $message .= sprintf(' - %d campaigns failed', $stats['campaigns_failed']);
                }

                Notification::make()
                    ->title('Bulk Video Sync Completed')
                    ->body($message)
                    ->success()
                    ->duration(10000)
                    ->send();

            } else {
                Notification::make()
                    ->title('Bulk Video Sync Failed')
                    ->body('Failed to sync videos for the selected campaigns.')
                    ->danger()
                    ->send();
            }

        } catch (Exception $e) {
            Notification::make()
                ->title('Direct Sync Error')
                ->body('Direct sync failed: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }
}
