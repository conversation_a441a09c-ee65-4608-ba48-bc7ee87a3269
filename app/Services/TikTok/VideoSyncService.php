<?php

namespace App\Services\TikTok;

use App\Models\Video;
use App\Models\Campaign;
use App\Models\Shop;
use App\Models\Identity;
use App\Helpers\ErrorHandler;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Exception;

/**
 * Service chuyên biệt để đồng bộ video data từ TikTok API
 */
class VideoSyncService
{
    private TikTokApiService $apiService;

    public function __construct(TikTokApiService $apiService)
    {
        $this->apiService = $apiService;
    }

    /**
     * Sync videos for a specific campaign
     *
     * @param Campaign $campaign Campaign to sync videos for
     * @param array $options Additional options for video retrieval
     * @return array Sync result with statistics
     */
    public function syncVideosForCampaign(Campaign $campaign, array $options = []): array
    {
        try {
            // Set advertiser ID in API service
            $this->apiService->setAdvertiserId($campaign->advertiser_id);

            // Get store information from shop relationship
            $shop = $campaign->shop;
            if (!$shop) {
                return ErrorHandler::createErrorResponse(
                    'Campaign missing shop relationship',
                    ErrorHandler::VALIDATION_ERROR,
                    ['campaign_id' => $campaign->campaign_id]
                );
            }

            // Validate shop has required store information
            if (!$shop->shop_id || !$shop->store_authorized_bc_id) {
                return ErrorHandler::createErrorResponse(
                    'Shop missing required store information',
                    ErrorHandler::VALIDATION_ERROR,
                    [
                        'campaign_id' => $campaign->campaign_id,
                        'shop_id' => $shop->id,
                        'missing_shop_id' => !$shop->shop_id,
                        'missing_store_authorized_bc_id' => !$shop->store_authorized_bc_id
                    ]
                );
            }

            // Build API options using shop data
            $apiOptions = array_merge([
                'store_id' => $shop->shop_id,
                'store_authorized_bc_id' => $shop->store_authorized_bc_id,
                'page' => 1,
                'page_size' => 50,
            ], $options);

            // Add identity list if available in campaign
            if ($campaign->identity_list && is_array($campaign->identity_list)) {
                $apiOptions['identity_list'] = $campaign->identity_list;
            }

            // Add product filters if available
            if ($campaign->item_group_ids && is_array($campaign->item_group_ids)) {
                $apiOptions['spu_id_list'] = array_slice($campaign->item_group_ids, 0, 50);
            }

            $allVideos = [];
            $currentPage = 1;
            $totalPages = 1;

            // Fetch all pages of videos
            do {
                $apiOptions['page'] = $currentPage;
                $response = $this->apiService->getVideos($apiOptions);

                if (!ErrorHandler::isSuccess($response)) {
                    return $response;
                }

                $data = $response['data']['data'] ?? [];
                $pageInfo = $data['page_info'] ?? [];

                $videos = $data['item_list'] ?? [];
                $allVideos = array_merge($allVideos, $videos);

                $totalPages = $pageInfo['total_page'] ?? 1;
                $currentPage++;

            } while ($currentPage <= $totalPages && $currentPage <= 10); // Limit to 10 pages max

            // Process and save videos
            return $this->processAndSaveVideos($campaign, $allVideos);

        } catch (Exception $e) {
            Log::error('Failed to sync videos for campaign', [
                'campaign_id' => $campaign->campaign_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return ErrorHandler::createErrorResponse(
                'Video sync failed: ' . $e->getMessage(),
                ErrorHandler::SERVICE_ERROR,
                ['campaign_id' => $campaign->campaign_id]
            );
        }
    }

    /**
     * Process and save videos to database
     *
     * @param Campaign $campaign Campaign the videos belong to
     * @param array $videos Array of video data from API
     * @return array Processing result with statistics
     */
    private function processAndSaveVideos(Campaign $campaign, array $videos): array
    {
        $stats = [
            'total' => count($videos),
            'created' => 0,
            'updated' => 0,
            'skipped' => 0,
            'errors' => 0
        ];

        DB::beginTransaction();

        try {
            foreach ($videos as $videoData) {
                $result = $this->processSingleVideo($campaign, $videoData);
                $stats[$result]++;
            }

            DB::commit();

            return ErrorHandler::createSuccessResponse([
                'stats' => $stats,
                'campaign_id' => $campaign->campaign_id
            ], "Successfully processed {$stats['total']} videos");

        } catch (Exception $e) {
            DB::rollBack();

            Log::error('Failed to process videos', [
                'campaign_id' => $campaign->campaign_id,
                'error' => $e->getMessage()
            ]);

            return ErrorHandler::createErrorResponse(
                'Failed to save videos: ' . $e->getMessage(),
                ErrorHandler::DATABASE_ERROR,
                ['campaign_id' => $campaign->campaign_id, 'stats' => $stats]
            );
        }
    }

    /**
     * Process a single video and save to database
     *
     * @param Campaign $campaign Campaign the video belongs to
     * @param array $videoData Video data from API
     * @return string Result type: 'created', 'updated', 'skipped', or 'errors'
     */
    private function processSingleVideo(Campaign $campaign, array $videoData): string
    {
        try {
            $itemId = $videoData['item_id'] ?? null;
            if (!$itemId) {
                Log::warning('Video missing item_id', ['video_data' => $videoData]);
                return 'skipped';
            }

            // Extract video information
            $videoInfo = $videoData['video_info'] ?? [];
            $identityInfo = $videoData['identity_info'] ?? [];

            // Prepare video data for database
            $videoDbData = [
                'video_id' => $itemId,
                'campaign_id' => $campaign->campaign_id,
                'title' => $videoData['text'] ?? '',
                'url' => $videoInfo['preview_url'] ?? '',
                'thumbnail' => $videoInfo['video_cover_url'] ?? '',
                'duration' => (int)($videoInfo['duration'] ?? 0),
                'status' => 'active',
                'is_custom_anchor' => $videoData['can_change_anchor'] ?? false,
                // Additional fields from API
                'spu_id_list' => json_encode($videoData['spu_id_list'] ?? []),
                'identity_info' => json_encode($identityInfo),
                'video_info' => json_encode($videoInfo),
            ];

            // Check if video already exists
            $existingVideo = Video::where('video_id', $itemId)
                ->where('campaign_id', $campaign->campaign_id)
                ->first();

            if ($existingVideo) {
                $existingVideo->update($videoDbData);
                return 'updated';
            } else {
                Video::create($videoDbData);
                return 'created';
            }

        } catch (Exception $e) {
            Log::error('Failed to process single video', [
                'campaign_id' => $campaign->campaign_id,
                'video_data' => $videoData,
                'error' => $e->getMessage()
            ]);
            return 'errors';
        }
    }

    /**
     * Sync videos for multiple campaigns
     *
     * @param array $campaignIds Array of campaign IDs to sync
     * @param array $options Additional options for video retrieval
     * @return array Sync result with statistics for all campaigns
     */
    public function syncVideosForMultipleCampaigns(array $campaignIds, array $options = []): array
    {
        Log::debug('Starting sync for multiple campaigns', [
            'campaign_ids' => $campaignIds,
            'campaign_count' => count($campaignIds),
            'options' => $options
        ]);

        $results = [];
        $overallStats = [
            'campaigns_processed' => 0,
            'campaigns_success' => 0,
            'campaigns_failed' => 0,
            'total_videos' => 0,
            'total_created' => 0,
            'total_updated' => 0,
        ];

        foreach ($campaignIds as $campaignId) {
            Log::debug('Processing campaign for video sync', [
                'campaign_id' => $campaignId
            ]);

            try {
                // Add retry mechanism for database queries
                $campaign = null;
                $maxRetries = 3;
                $retryCount = 0;

                while ($retryCount < $maxRetries && !$campaign) {
                    try {
                        $campaign = Campaign::where('campaign_id', $campaignId)->with('shop')->first();
                        if ($campaign) {
                            break;
                        }

                        if ($retryCount < $maxRetries - 1) {
                            Log::debug('Campaign not found, retrying...', [
                                'campaign_id' => $campaignId,
                                'retry_count' => $retryCount + 1
                            ]);
                            sleep(1); // Wait 1 second before retry
                        }
                    } catch (\Exception $e) {
                        Log::error('Database error while searching campaign', [
                            'campaign_id' => $campaignId,
                            'retry_count' => $retryCount + 1,
                            'error' => $e->getMessage()
                        ]);

                        if ($retryCount < $maxRetries - 1) {
                            sleep(2); // Wait longer on database errors
                        }
                    }
                    $retryCount++;
                }

                if (!$campaign) {
                    Log::warning('Campaign not found in database after retries', [
                        'campaign_id' => $campaignId,
                        'searched_field' => 'campaign_id',
                        'retries_attempted' => $retryCount
                    ]);

                    // Debug: Check if campaign exists with different field
                    $campaignById = Campaign::find($campaignId);
                    $campaignByName = Campaign::where('name', 'like', "%{$campaignId}%")->first();
                    $totalCampaigns = Campaign::count();

                    Log::debug('Alternative campaign search results', [
                        'campaign_id' => $campaignId,
                        'found_by_id' => $campaignById ? $campaignById->campaign_id : null,
                        'found_by_name' => $campaignByName ? $campaignByName->campaign_id : null,
                        'total_campaigns_in_db' => $totalCampaigns
                    ]);

                    $results[$campaignId] = [
                        'success' => false,
                        'error' => 'Campaign not found'
                    ];
                    $overallStats['campaigns_failed']++;
                    continue;
                }
            } catch (\Exception $e) {
                Log::error('Unexpected error while processing campaign', [
                    'campaign_id' => $campaignId,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);

                $results[$campaignId] = [
                    'success' => false,
                    'error' => 'Database error: ' . $e->getMessage()
                ];
                $overallStats['campaigns_failed']++;
                continue;
            }

            Log::debug('Campaign found, checking shop details', [
                'campaign_id' => $campaignId,
                'campaign_name' => $campaign->name,
                'shop_id' => $campaign->shop_id,
                'shop_name' => $campaign->shop->name ?? null,
                'store_authorized_bc_id' => $campaign->shop->store_authorized_bc_id ?? null
            ]);

            $result = $this->syncVideosForCampaign($campaign, $options);
            $results[$campaignId] = $result;
            $overallStats['campaigns_processed']++;

            if (ErrorHandler::isSuccess($result)) {
                $overallStats['campaigns_success']++;
                $stats = $result['data']['stats'] ?? [];
                $overallStats['total_videos'] += $stats['total'] ?? 0;
                $overallStats['total_created'] += $stats['created'] ?? 0;
                $overallStats['total_updated'] += $stats['updated'] ?? 0;

                Log::debug('Campaign sync successful', [
                    'campaign_id' => $campaignId,
                    'stats' => $stats
                ]);
            } else {
                $overallStats['campaigns_failed']++;
                Log::warning('Campaign sync failed', [
                    'campaign_id' => $campaignId,
                    'error' => $result['message'] ?? 'Unknown error'
                ]);
            }
        }

        return [
            'overall_stats' => $overallStats,
            'campaign_results' => $results
        ];
    }

    /**
     * Sync videos for all active campaigns
     *
     * @param array $options Additional options for video retrieval
     * @return array Sync result with statistics for all active campaigns
     */
    public function syncVideosForAllActiveCampaigns(array $options = []): array
    {
        Log::debug('Starting sync for all active campaigns', [
            'options' => $options,
            'timestamp' => now()->toISOString()
        ]);

        // Log database state for debugging
        $this->logDatabaseState();

        $activeCampaigns = Campaign::where('status', 'active')
            ->whereNotNull('shop_id')
            ->whereHas('shop', function ($query) {
                $query->whereNotNull('store_authorized_bc_id');
            })
            ->pluck('campaign_id')
            ->toArray();

        Log::debug('Active campaigns query result', [
            'campaign_ids' => $activeCampaigns,
            'count' => count($activeCampaigns)
        ]);

        if (empty($activeCampaigns)) {
            Log::warning('No active campaigns found to sync');
            return [
                'success' => true,
                'message' => 'No active campaigns found to sync',
                'overall_stats' => [
                    'campaigns_processed' => 0,
                    'campaigns_success' => 0,
                    'campaigns_failed' => 0,
                    'total_videos' => 0,
                    'total_created' => 0,
                    'total_updated' => 0,
                ]
            ];
        }

        return $this->syncVideosForMultipleCampaigns($activeCampaigns, $options);
    }

    /**
     * Get sync statistics for a campaign
     *
     * @param Campaign $campaign Campaign to get stats for
     * @return array Statistics about videos for the campaign
     */
    public function getCampaignVideoStats(Campaign $campaign): array
    {
        $totalVideos = $campaign->videos()->count();
        $activeVideos = $campaign->videos()->where('status', 'active')->count();
        $customAnchorVideos = $campaign->videos()->where('can_change_anchor', true)->count();
        $recentVideos = $campaign->videos()->where('created_at', '>=', now()->subDays(7))->count();

        return [
            'total_videos' => $totalVideos,
            'active_videos' => $activeVideos,
            'custom_anchor_videos' => $customAnchorVideos,
            'recent_videos' => $recentVideos,
            'last_sync' => $campaign->videos()->latest('updated_at')->value('updated_at'),
        ];
    }

    /**
     * Check if campaign should be synced based on last sync time
     *
     * @param Campaign $campaign Campaign to check
     * @param int $hoursThreshold Minimum hours between syncs
     * @return bool Whether campaign should be synced
     */
    public function shouldSyncCampaign(Campaign $campaign, int $hoursThreshold = 6): bool
    {
        $lastSync = $campaign->videos()->latest('updated_at')->value('updated_at');

        if (!$lastSync) {
            return true; // Never synced before
        }

        return now()->diffInHours($lastSync) >= $hoursThreshold;
    }

    /**
     * Clean up old or invalid videos
     *
     * @param Campaign $campaign Campaign to clean up videos for
     * @param int $daysOld Remove videos older than this many days
     * @return array Cleanup result with statistics
     */
    public function cleanupOldVideos(Campaign $campaign, int $daysOld = 30): array
    {
        try {
            $cutoffDate = now()->subDays($daysOld);

            $oldVideos = $campaign->videos()
                ->where('created_at', '<', $cutoffDate)
                ->where('status', '!=', 'active')
                ->get();

            $deletedCount = 0;
            foreach ($oldVideos as $video) {
                $video->deleteThumbnailFile(); // Clean up thumbnail file
                $video->delete();
                $deletedCount++;
            }

            return [
                'success' => true,
                'deleted_count' => $deletedCount,
                'message' => "Cleaned up {$deletedCount} old videos"
            ];

        } catch (\Exception $e) {
            Log::error('Failed to cleanup old videos', [
                'campaign_id' => $campaign->campaign_id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'deleted_count' => 0,
                'message' => 'Cleanup failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Log database state for debugging
     */
    private function logDatabaseState(): void
    {
        try {
            // Check database connection
            $connectionName = DB::connection()->getName();

            // Get campaign counts
            $totalCampaigns = Campaign::count();
            $activeCampaignsCount = Campaign::where('status', 'active')->count();
            $campaignsWithShop = Campaign::where('status', 'active')->whereNotNull('shop_id')->count();

            // Get shop counts
            $totalShops = Shop::count();
            $shopsWithAuth = Shop::whereNotNull('store_authorized_bc_id')->count();

            // Get sample campaign IDs for verification
            $sampleCampaigns = Campaign::where('status', 'active')
                ->whereNotNull('shop_id')
                ->take(5)
                ->get(['id', 'campaign_id', 'name', 'status', 'shop_id'])
                ->toArray();

            Log::debug('Database state snapshot', [
                'connection' => $connectionName,
                'timestamp' => now()->toISOString(),
                'campaigns' => [
                    'total' => $totalCampaigns,
                    'active' => $activeCampaignsCount,
                    'active_with_shop' => $campaignsWithShop,
                    'sample_campaigns' => $sampleCampaigns
                ],
                'shops' => [
                    'total' => $totalShops,
                    'with_store_auth' => $shopsWithAuth
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to log database state', [
                'error' => $e->getMessage()
            ]);
        }
    }
}
